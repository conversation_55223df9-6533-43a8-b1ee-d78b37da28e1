/**
 * Supabase Client for Real-time Features
 * Handles client-side Supabase connections for real-time messaging and notifications
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseConfig } from './config';

// Database types for real-time tables
export interface MessageRealtime {
  id: string;
  mysql_message_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  read_at?: string;
}

export interface NotificationRealtime {
  id: string;
  mysql_notification_id: string;
  recipient_id: string;
  type: string;
  data: any;
  created_at: string;
  read_at?: string;
}

export interface UserPresence {
  user_id: string;
  status: 'online' | 'offline' | 'away';
  last_seen: string;
}

// Database schema type
export interface Database {
  public: {
    Tables: {
      messages_realtime: {
        Row: MessageRealtime;
        Insert: Omit<MessageRealtime, 'id' | 'created_at'>;
        Update: Partial<Omit<MessageRealtime, 'id'>>;
      };
      notifications_realtime: {
        Row: NotificationRealtime;
        Insert: Omit<NotificationRealtime, 'id' | 'created_at'>;
        Update: Partial<Omit<NotificationRealtime, 'id'>>;
      };
      user_presence: {
        Row: UserPresence;
        Insert: Omit<UserPresence, 'last_seen'>;
        Update: Partial<UserPresence>;
      };
    };
  };
}

let supabaseClient: SupabaseClient<Database> | null = null;

/**
 * Get Supabase client instance (singleton pattern)
 */
export function getSupabaseClient(): SupabaseClient<Database> {
  if (!supabaseClient) {
    const config = getSupabaseConfig();
    supabaseClient = createClient<Database>(config.url, config.anonKey, {
      realtime: {
        params: {
          eventsPerSecond: 10, // Rate limiting for real-time events
        },
        heartbeatIntervalMs: 30000, // 30 seconds heartbeat
        reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 30000), // Progressive reconnect delay
        timeout: 20000, // 20 seconds timeout
        transport: 'websocket',
      },
      auth: {
        persistSession: false, // We handle auth through NextAuth
      },
      global: {
        headers: {
          'x-client-info': 'hifnf-realtime-client',
        },
      },
    });
  }

  return supabaseClient;
}

/**
 * Get Supabase client with service role (for server-side operations)
 */
export function getSupabaseServiceClient(): SupabaseClient<Database> {
  const config = getSupabaseConfig();
  return createClient<Database>(config.url, config.serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

/**
 * Test Supabase connection
 */
export async function testSupabaseConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    const client = getSupabaseClient();
    const { error } = await client.from('user_presence').select('count').limit(1);
    
    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
